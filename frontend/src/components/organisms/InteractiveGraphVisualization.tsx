import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { GraphData, GraphNode, GraphRelationship } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Filter, 
  Search,
  Download,
  Maximize2,
  Grid3X3,
  Network
} from 'lucide-react';

interface InteractiveGraphVisualizationProps {
  data: GraphData;
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode) => void;
  onRelationshipClick?: (relationship: GraphRelationship) => void;
  className?: string;
}

interface FilterState {
  nodeTypes: string[];
  relationshipTypes: string[];
  strengthLevels: string[];
  searchQuery: string;
}

type VisualizationMode = 'network' | 'hierarchical' | 'circular' | 'force';

const InteractiveGraphVisualization: React.FC<InteractiveGraphVisualizationProps> = ({
  data,
  width = 1200,
  height = 800,
  onNodeClick,
  onRelationshipClick,
  className = ''
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [filteredData, setFilteredData] = useState<GraphData>(data);
  const [filters, setFilters] = useState<FilterState>({
    nodeTypes: [],
    relationshipTypes: [],
    strengthLevels: [],
    searchQuery: ''
  });
  const [visualizationMode, setVisualizationMode] = useState<VisualizationMode>('force');
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // D3 simulation and elements
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphRelationship> | null>(null);
  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);

  // Color scales for different node types
  const nodeColorScale = d3.scaleOrdinal<string>()
    .domain(['Vitamins', 'Minerals', 'Omega-3', 'Probiotics', 'Amino Acids', 'Herbs', 'Adaptogens', 'Health Areas', 'Effects'])
    .range(['#3b82f6', '#10b981', '#8b5cf6', '#14b8a6', '#f97316', '#10b981', '#047857', '#ef4444', '#f59e0b']);

  // Relationship color scale
  const relationshipColorScale = d3.scaleOrdinal<string>()
    .domain(['SUPPORTS', 'PRODUCES', 'ENHANCES_ABSORPTION', 'SYNERGISTIC_WITH', 'REQUIRES', 'COMPETES_WITH', 'CONTRAINDICATED_WITH'])
    .range(['#10b981', '#059669', '#047857', '#065f46', '#3b82f6', '#f59e0b', '#dc2626']);

  // Filter data based on current filters
  const applyFilters = useCallback(() => {
    let filteredNodes = data.nodes;
    let filteredRelationships = data.relationships;

    // Apply node type filters
    if (filters.nodeTypes.length > 0) {
      filteredNodes = filteredNodes.filter(node => 
        filters.nodeTypes.some(type => node.properties.category === type)
      );
    }

    // Apply search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filteredNodes = filteredNodes.filter(node =>
        node.properties.name?.toLowerCase().includes(query) ||
        node.properties.description?.toLowerCase().includes(query)
      );
    }

    // Filter relationships to only include those between filtered nodes
    const nodeIds = new Set(filteredNodes.map(n => n.id));
    filteredRelationships = filteredRelationships.filter(rel =>
      nodeIds.has(rel.source) && nodeIds.has(rel.target)
    );

    // Apply relationship type filters
    if (filters.relationshipTypes.length > 0) {
      filteredRelationships = filteredRelationships.filter(rel =>
        filters.relationshipTypes.includes(rel.type)
      );
    }

    // Apply strength level filters
    if (filters.strengthLevels.length > 0) {
      filteredRelationships = filteredRelationships.filter(rel =>
        filters.strengthLevels.includes(rel.properties.strength || 'medium')
      );
    }

    setFilteredData({ nodes: filteredNodes, relationships: filteredRelationships });
  }, [data, filters]);

  // Initialize and update visualization
  useEffect(() => {
    if (!svgRef.current || !filteredData.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const container = svg.append('g').attr('class', 'graph-container');

    // Setup zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    svg.call(zoom);
    zoomRef.current = zoom;

    // Create simulation based on visualization mode
    let simulation: d3.Simulation<GraphNode, GraphRelationship>;

    switch (visualizationMode) {
      case 'force':
        simulation = d3.forceSimulation(filteredData.nodes)
          .force('link', d3.forceLink(filteredData.relationships)
            .id((d: any) => d.id)
            .distance(100)
            .strength(0.5))
          .force('charge', d3.forceManyBody().strength(-300))
          .force('center', d3.forceCenter(width / 2, height / 2))
          .force('collision', d3.forceCollide().radius(30));
        break;

      case 'hierarchical':
        // Implement hierarchical layout
        const hierarchy = d3.stratify<GraphNode>()
          .id(d => d.id)
          .parentId(d => {
            // Find parent based on relationships
            const parentRel = filteredData.relationships.find(r => r.target === d.id);
            return parentRel?.source;
          });

        try {
          const root = hierarchy(filteredData.nodes);
          const treeLayout = d3.tree<GraphNode>().size([width - 100, height - 100]);
          treeLayout(root);

          // Position nodes based on tree layout
          filteredData.nodes.forEach(node => {
            const treeNode = root.descendants().find(d => d.id === node.id);
            if (treeNode) {
              (node as any).x = treeNode.x + 50;
              (node as any).y = treeNode.y + 50;
              (node as any).fx = treeNode.x + 50;
              (node as any).fy = treeNode.y + 50;
            }
          });

          simulation = d3.forceSimulation(filteredData.nodes)
            .force('link', d3.forceLink(filteredData.relationships).id((d: any) => d.id))
            .alphaDecay(1); // Stop simulation immediately
        } catch {
          // Fallback to force layout if hierarchy fails
          simulation = d3.forceSimulation(filteredData.nodes)
            .force('link', d3.forceLink(filteredData.relationships).id((d: any) => d.id))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2));
        }
        break;

      case 'circular':
        // Arrange nodes in a circle
        filteredData.nodes.forEach((node, i) => {
          const angle = (2 * Math.PI * i) / filteredData.nodes.length;
          const radius = Math.min(width, height) / 3;
          (node as any).x = width / 2 + radius * Math.cos(angle);
          (node as any).y = height / 2 + radius * Math.sin(angle);
          (node as any).fx = (node as any).x;
          (node as any).fy = (node as any).y;
        });

        simulation = d3.forceSimulation(filteredData.nodes)
          .force('link', d3.forceLink(filteredData.relationships).id((d: any) => d.id))
          .alphaDecay(1);
        break;

      default:
        simulation = d3.forceSimulation(filteredData.nodes)
          .force('link', d3.forceLink(filteredData.relationships).id((d: any) => d.id))
          .force('charge', d3.forceManyBody().strength(-300))
          .force('center', d3.forceCenter(width / 2, height / 2));
    }

    simulationRef.current = simulation;

    // Create links
    const links = container.selectAll('.link')
      .data(filteredData.relationships)
      .enter().append('line')
      .attr('class', 'link')
      .attr('stroke', d => relationshipColorScale(d.type))
      .attr('stroke-width', d => {
        const strength = d.properties.strength || 'medium';
        return strength === 'high' ? 3 : strength === 'medium' ? 2 : 1.5;
      })
      .attr('stroke-opacity', d => {
        const strength = d.properties.strength || 'medium';
        return strength === 'high' ? 0.8 : strength === 'medium' ? 0.6 : 0.4;
      })
      .attr('stroke-dasharray', d => d.type.includes('CONTRAINDICATED') ? '5,5' : 'none')
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation();
        onRelationshipClick?.(d);
      });

    // Create nodes
    const nodes = container.selectAll('.node')
      .data(filteredData.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .call(d3.drag<SVGGElement, GraphNode>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          (d as any).fx = (d as any).x;
          (d as any).fy = (d as any).y;
        })
        .on('drag', (event, d) => {
          (d as any).fx = event.x;
          (d as any).fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          (d as any).fx = null;
          (d as any).fy = null;
        }));

    // Add circles to nodes
    nodes.append('circle')
      .attr('r', d => {
        const category = d.properties.category || 'Effects';
        return category === 'Health Areas' ? 20 : category === 'Effects' ? 15 : 12;
      })
      .attr('fill', d => nodeColorScale(d.properties.category || 'Effects'))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedNode(d);
        onNodeClick?.(d);
      });

    // Add labels to nodes
    nodes.append('text')
      .text(d => d.properties.name || d.id)
      .attr('dy', -25)
      .attr('text-anchor', 'middle')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .attr('fill', '#374151')
      .style('pointer-events', 'none');

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      nodes.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
    });

    return () => {
      simulation.stop();
    };
  }, [filteredData, visualizationMode, width, height, onNodeClick, onRelationshipClick]);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const handleZoomIn = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1.5
      );
    }
  };

  const handleZoomOut = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1 / 1.5
      );
    }
  };

  const handleReset = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.transform,
        d3.zoomIdentity
      );
    }
    if (simulationRef.current) {
      simulationRef.current.alpha(1).restart();
    }
  };

  const handleExport = () => {
    if (svgRef.current) {
      const svgData = new XMLSerializer().serializeToString(svgRef.current);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = 'supplement-knowledge-graph.svg';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  };

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      {/* Controls */}
      <Card className="absolute top-4 left-4 z-10 bg-white/90 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Graph Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <Search className="w-4 h-4 text-gray-500" />
            <Input
              placeholder="Search nodes..."
              value={filters.searchQuery}
              onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="w-40"
            />
          </div>

          {/* Visualization Mode */}
          <Select value={visualizationMode} onValueChange={(value: VisualizationMode) => setVisualizationMode(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="force">Force Layout</SelectItem>
              <SelectItem value="hierarchical">Hierarchical</SelectItem>
              <SelectItem value="circular">Circular</SelectItem>
            </SelectContent>
          </Select>

          {/* Zoom Controls */}
          <div className="flex space-x-1">
            <Button size="sm" variant="outline" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>

          {/* Export */}
          <Button size="sm" variant="outline" onClick={handleExport} className="w-full">
            <Download className="w-4 h-4 mr-2" />
            Export SVG
          </Button>
        </CardContent>
      </Card>

      {/* Node Details Panel */}
      {selectedNode && (
        <Card className="absolute top-4 right-4 z-10 w-80 bg-white/90 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">{selectedNode.properties.name}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Badge variant="secondary">{selectedNode.properties.category}</Badge>
            {selectedNode.properties.description && (
              <p className="text-sm text-gray-600">{selectedNode.properties.description}</p>
            )}
            {selectedNode.properties.dosageRange && (
              <div className="text-sm">
                <strong>Dosage:</strong> {selectedNode.properties.dosageRange}
              </div>
            )}
            {selectedNode.properties.safetyLevel && (
              <div className="text-sm">
                <strong>Safety:</strong> {selectedNode.properties.safetyLevel}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Visualization */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded-lg bg-white"
        style={{ minHeight: '600px' }}
      />

      {/* Stats */}
      <div className="absolute bottom-4 left-4 z-10 bg-white/90 backdrop-blur-sm rounded-lg p-3">
        <div className="text-sm text-gray-600">
          <div>Nodes: {filteredData.nodes.length}</div>
          <div>Relationships: {filteredData.relationships.length}</div>
        </div>
      </div>
    </div>
  );
};

export default InteractiveGraphVisualization;

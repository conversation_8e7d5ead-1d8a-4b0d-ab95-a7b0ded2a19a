import React, { useState, useMemo } from 'react';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import GraphVisualization from '@/components/organisms/GraphVisualization';
import GraphLegend from '@/components/organisms/GraphLegend';
import { 
  supplementKnowledgeGraph, 
  supplementCategories, 
  relationshipTypes 
} from '@/data/supplementKnowledgeGraph';
import { GraphData, GraphNode, GraphRelationship } from '@/types';
import { 
  Filter, 
  Info, 
  Zap, 
  Heart, 
  Shield, 
  Brain,
  Pill,
  Leaf,
  Droplets,
  Activity
} from 'lucide-react';

const SupplementKnowledgePage: React.FC = () => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>(supplementCategories);
  const [selectedRelationshipTypes, setSelectedRelationshipTypes] = useState<string[]>(relationshipTypes);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);

  // Filter data based on selected categories and relationship types
  const filteredData = useMemo((): GraphData => {
    const filteredNodes = supplementKnowledgeGraph.nodes.filter(node => 
      selectedCategories.includes(node.properties.category || 'Unknown')
    );
    
    const nodeIds = new Set(filteredNodes.map(node => node.id));
    
    const filteredRelationships = supplementKnowledgeGraph.relationships.filter(rel => 
      selectedRelationshipTypes.includes(rel.type) &&
      nodeIds.has(typeof rel.source === 'string' ? rel.source : rel.source.id) &&
      nodeIds.has(typeof rel.target === 'string' ? rel.target : rel.target.id)
    );

    return {
      nodes: filteredNodes,
      relationships: filteredRelationships
    };
  }, [selectedCategories, selectedRelationshipTypes]);

  const toggleCategory = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const toggleRelationshipType = (type: string) => {
    setSelectedRelationshipTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Vitamins': return <Pill className="w-4 h-4" />;
      case 'Minerals': return <Droplets className="w-4 h-4" />;
      case 'Omega Fatty Acids': return <Heart className="w-4 h-4" />;
      case 'Probiotics': return <Shield className="w-4 h-4" />;
      case 'Amino Acids': return <Zap className="w-4 h-4" />;
      case 'Herbal Supplements': return <Leaf className="w-4 h-4" />;
      case 'Adaptogens': return <Brain className="w-4 h-4" />;
      case 'Health Areas': return <Activity className="w-4 h-4" />;
      case 'Effects': return <Info className="w-4 h-4" />;
      default: return <Pill className="w-4 h-4" />;
    }
  };

  const getRelationshipColor = (type: string) => {
    const colors = {
      'SUPPORTS': 'bg-green-100 text-green-800',
      'PRODUCES': 'bg-green-200 text-green-900',
      'ENHANCES_ABSORPTION': 'bg-emerald-100 text-emerald-800',
      'SYNERGISTIC_WITH': 'bg-emerald-200 text-emerald-900',
      'REQUIRES': 'bg-blue-100 text-blue-800',
      'COMPETES_WITH': 'bg-yellow-100 text-yellow-800',
      'INTERACTS_WITH': 'bg-red-100 text-red-800',
      'CONTRAINDICATED_WITH': 'bg-red-200 text-red-900',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
  };

  const handleNodeHover = (node: GraphNode | null) => {
    setHoveredNode(node);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
            Supplement Knowledge Graph
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Explore the complex relationships between supplements, their benefits, and potential interactions. 
            This interactive visualization helps you understand how different supplements work together.
          </p>
        </div>

        <Tabs defaultValue="visualization" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="visualization">Interactive Graph</TabsTrigger>
            <TabsTrigger value="legend">Legend</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="relationships">Relationships</TabsTrigger>
          </TabsList>

          <TabsContent value="visualization" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Filters Sidebar */}
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Filter className="w-5 h-5" />
                    Filters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Category Filters */}
                  <div>
                    <h4 className="font-semibold mb-2">Categories</h4>
                    <div className="space-y-2">
                      {supplementCategories.map(category => (
                        <Button
                          key={category}
                          variant={selectedCategories.includes(category) ? "primary" : "outline"}
                          size="sm"
                          onClick={() => toggleCategory(category)}
                          className="w-full justify-start text-xs"
                        >
                          {getCategoryIcon(category)}
                          <span className="ml-2 truncate">{category}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Relationship Type Filters */}
                  <div>
                    <h4 className="font-semibold mb-2">Relationships</h4>
                    <div className="space-y-1">
                      {relationshipTypes.map(type => (
                        <Button
                          key={type}
                          variant={selectedRelationshipTypes.includes(type) ? "primary" : "outline"}
                          size="sm"
                          onClick={() => toggleRelationshipType(type)}
                          className="w-full justify-start text-xs"
                        >
                          <span className="truncate">{type.replace(/_/g, ' ')}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="pt-4 border-t">
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>Nodes: {filteredData.nodes.length}</div>
                      <div>Relationships: {filteredData.relationships.length}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Main Visualization */}
              <Card className="lg:col-span-3">
                <CardContent className="p-6">
                  <div className="h-[600px] w-full">
                    <GraphVisualization
                      data={filteredData}
                      width={800}
                      height={600}
                      nodeSize={12}
                      linkDistance={100}
                      chargeStrength={-300}
                      onNodeClick={handleNodeClick}
                      onNodeHover={handleNodeHover}
                      className="w-full h-full border rounded-lg"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Node Details Panel */}
            {selectedNode && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getCategoryIcon(selectedNode.properties.category || '')}
                    {selectedNode.properties.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Description</h4>
                      <p className="text-sm text-gray-600 mb-4">
                        {selectedNode.properties.description}
                      </p>
                      
                      <div className="space-y-2">
                        <div>
                          <span className="font-medium">Category: </span>
                          <Badge variant="secondary">{selectedNode.properties.category}</Badge>
                        </div>
                        {selectedNode.properties.targetHealthArea && (
                          <div>
                            <span className="font-medium">Target Health Area: </span>
                            <span className="text-sm">{selectedNode.properties.targetHealthArea}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      {selectedNode.properties.commonUses && (
                        <div className="mb-3">
                          <h4 className="font-semibold mb-1">Common Uses</h4>
                          <p className="text-sm text-gray-600">{selectedNode.properties.commonUses}</p>
                        </div>
                      )}
                      
                      {selectedNode.properties.dosageRange && (
                        <div className="mb-3">
                          <h4 className="font-semibold mb-1">Dosage Range</h4>
                          <p className="text-sm text-gray-600">{selectedNode.properties.dosageRange}</p>
                        </div>
                      )}
                      
                      {selectedNode.properties.safetyLevel && (
                        <div>
                          <h4 className="font-semibold mb-1">Safety Level</h4>
                          <Badge 
                            variant={selectedNode.properties.safetyLevel === 'Very Safe' ? 'primary' : 'secondary'}
                          >
                            {selectedNode.properties.safetyLevel}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="legend">
            <div className="max-w-4xl mx-auto">
              <GraphLegend />
            </div>
          </TabsContent>

          <TabsContent value="categories">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {supplementCategories.map(category => {
                const categoryNodes = supplementKnowledgeGraph.nodes.filter(
                  node => node.properties.category === category
                );
                
                return (
                  <Card key={category}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        {getCategoryIcon(category)}
                        {category}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-3">
                        {categoryNodes.length} supplements in this category
                      </p>
                      <div className="space-y-2">
                        {categoryNodes.slice(0, 3).map(node => (
                          <div key={node.id} className="text-sm">
                            <span className="font-medium">{node.properties.name}</span>
                          </div>
                        ))}
                        {categoryNodes.length > 3 && (
                          <div className="text-xs text-gray-500">
                            +{categoryNodes.length - 3} more...
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="relationships">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {relationshipTypes.map(type => {
                const typeRelationships = supplementKnowledgeGraph.relationships.filter(
                  rel => rel.type === type
                );
                
                return (
                  <Card key={type}>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {type.replace(/_/g, ' ')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Badge className={getRelationshipColor(type)}>
                        {typeRelationships.length} relationships
                      </Badge>
                      <div className="mt-3 space-y-2">
                        {typeRelationships.slice(0, 3).map(rel => {
                          const sourceNode = supplementKnowledgeGraph.nodes.find(n => n.id === rel.source);
                          const targetNode = supplementKnowledgeGraph.nodes.find(n => n.id === rel.target);
                          
                          return (
                            <div key={rel.id} className="text-sm">
                              <span className="font-medium">{sourceNode?.properties.name}</span>
                              <span className="text-gray-500 mx-2">→</span>
                              <span className="font-medium">{targetNode?.properties.name}</span>
                            </div>
                          );
                        })}
                        {typeRelationships.length > 3 && (
                          <div className="text-xs text-gray-500">
                            +{typeRelationships.length - 3} more...
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SupplementKnowledgePage;

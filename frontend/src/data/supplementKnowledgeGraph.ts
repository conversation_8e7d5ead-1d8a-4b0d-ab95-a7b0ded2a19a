import { GraphData, GraphNode, GraphRelationship } from '@/types';

// Comprehensive Supplement Knowledge Graph Data
// Based on research from FDA, NIH, and supplement databases

export const supplementNodes: GraphNode[] = [
  // VITAMINS
  {
    id: 'vitamin-c',
    labels: ['Vitamin', 'Antioxidant'],
    properties: {
      name: 'Vitamin C (Ascorbic Acid)',
      description: 'Essential water-soluble vitamin with powerful antioxidant properties',
      category: 'Vitamins',
      targetHealthArea: 'Immune System, Skin Health',
      commonUses: 'Immune support, collagen synthesis, wound healing',
      dosageRange: '65-2000mg daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Citrus fruits, berries, bell peppers, broccoli',
      mechanismOfAction: 'Antioxidant, collagen cofactor, immune modulator'
    }
  },
  {
    id: 'vitamin-d3',
    labels: ['Vitamin', 'Hormone'],
    properties: {
      name: 'Vitamin D3 (Cholecalciferol)',
      description: 'Fat-soluble vitamin essential for bone health and immune function',
      category: 'Vitamins',
      targetHealthArea: 'Bone Health, Immune System',
      commonUses: 'Bone health, immune support, mood regulation',
      dosageRange: '600-4000 IU daily',
      safetyLevel: 'Safe with monitoring',
      foodSources: 'Fatty fish, fortified milk, sunlight exposure',
      mechanismOfAction: 'Calcium absorption, immune regulation, gene expression'
    }
  },
  {
    id: 'vitamin-b12',
    labels: ['Vitamin', 'B-Complex'],
    properties: {
      name: 'Vitamin B12 (Cobalamin)',
      description: 'Essential vitamin for nerve function and red blood cell formation',
      category: 'Vitamins',
      targetHealthArea: 'Nervous System, Energy',
      commonUses: 'Energy production, nerve health, anemia prevention',
      dosageRange: '2.4-1000mcg daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Meat, fish, dairy, fortified cereals',
      mechanismOfAction: 'DNA synthesis, nerve myelination, energy metabolism'
    }
  },

  // MINERALS
  {
    id: 'zinc',
    labels: ['Mineral', 'Trace Element'],
    properties: {
      name: 'Zinc',
      description: 'Essential trace mineral for immune function and wound healing',
      category: 'Minerals',
      targetHealthArea: 'Immune System, Wound Healing',
      commonUses: 'Immune support, wound healing, taste/smell',
      dosageRange: '8-40mg daily',
      safetyLevel: 'Safe with limits',
      foodSources: 'Oysters, beef, pumpkin seeds, chickpeas',
      mechanismOfAction: 'Enzyme cofactor, immune cell function, protein synthesis'
    }
  },
  {
    id: 'magnesium',
    labels: ['Mineral', 'Electrolyte'],
    properties: {
      name: 'Magnesium',
      description: 'Essential mineral for muscle and nerve function',
      category: 'Minerals',
      targetHealthArea: 'Muscle Function, Sleep, Stress',
      commonUses: 'Muscle relaxation, sleep support, stress relief',
      dosageRange: '310-420mg daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Leafy greens, nuts, seeds, whole grains',
      mechanismOfAction: 'Enzyme cofactor, muscle relaxation, neurotransmitter regulation'
    }
  },
  {
    id: 'iron',
    labels: ['Mineral', 'Essential'],
    properties: {
      name: 'Iron',
      description: 'Essential mineral for oxygen transport and energy production',
      category: 'Minerals',
      targetHealthArea: 'Energy, Blood Health',
      commonUses: 'Anemia prevention, energy support, oxygen transport',
      dosageRange: '8-45mg daily',
      safetyLevel: 'Monitor levels',
      foodSources: 'Red meat, spinach, lentils, fortified cereals',
      mechanismOfAction: 'Oxygen transport, energy metabolism, DNA synthesis'
    }
  },

  // OMEGA FATTY ACIDS
  {
    id: 'omega-3-epa-dha',
    labels: ['Omega-3', 'Essential Fatty Acid'],
    properties: {
      name: 'Omega-3 (EPA/DHA)',
      description: 'Essential fatty acids with anti-inflammatory properties',
      category: 'Omega Fatty Acids',
      targetHealthArea: 'Heart Health, Brain Function',
      commonUses: 'Heart health, brain function, inflammation reduction',
      dosageRange: '250-2000mg daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Fatty fish, fish oil, algae oil',
      mechanismOfAction: 'Anti-inflammatory, membrane fluidity, neurotransmitter function'
    }
  },

  // PROBIOTICS
  {
    id: 'lactobacillus',
    labels: ['Probiotic', 'Beneficial Bacteria'],
    properties: {
      name: 'Lactobacillus',
      description: 'Beneficial bacteria supporting digestive and immune health',
      category: 'Probiotics',
      targetHealthArea: 'Digestive Health, Immune System',
      commonUses: 'Digestive support, immune health, antibiotic recovery',
      dosageRange: '1-100 billion CFU daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Yogurt, kefir, fermented foods, supplements',
      mechanismOfAction: 'Gut microbiome balance, immune modulation, pathogen inhibition'
    }
  },

  // AMINO ACIDS
  {
    id: 'creatine',
    labels: ['Amino Acid', 'Performance'],
    properties: {
      name: 'Creatine',
      description: 'Amino acid compound supporting muscle energy and performance',
      category: 'Amino Acids',
      targetHealthArea: 'Muscle Performance, Energy',
      commonUses: 'Athletic performance, muscle strength, brain function',
      dosageRange: '3-5g daily',
      safetyLevel: 'Very Safe',
      foodSources: 'Red meat, fish, supplements',
      mechanismOfAction: 'ATP regeneration, muscle energy, cellular hydration'
    }
  },

  // HERBAL SUPPLEMENTS
  {
    id: 'turmeric-curcumin',
    labels: ['Herb', 'Anti-inflammatory'],
    properties: {
      name: 'Turmeric (Curcumin)',
      description: 'Powerful anti-inflammatory herb with antioxidant properties',
      category: 'Herbal Supplements',
      targetHealthArea: 'Inflammation, Joint Health',
      commonUses: 'Inflammation reduction, joint health, antioxidant support',
      dosageRange: '500-1000mg daily',
      safetyLevel: 'Generally Safe',
      foodSources: 'Turmeric root, curry powder, supplements',
      mechanismOfAction: 'NF-kB inhibition, antioxidant activity, anti-inflammatory'
    }
  },
  {
    id: 'ashwagandha',
    labels: ['Herb', 'Adaptogen'],
    properties: {
      name: 'Ashwagandha',
      description: 'Adaptogenic herb for stress management and energy',
      category: 'Adaptogens',
      targetHealthArea: 'Stress Management, Energy',
      commonUses: 'Stress relief, energy support, sleep quality',
      dosageRange: '300-600mg daily',
      safetyLevel: 'Generally Safe',
      foodSources: 'Ashwagandha root extract supplements',
      mechanismOfAction: 'Cortisol regulation, stress response modulation, adaptogenic'
    }
  },

  // HEALTH CONDITIONS
  {
    id: 'immune-system',
    labels: ['Health Area', 'System'],
    properties: {
      name: 'Immune System',
      description: 'Body\'s defense system against infections and diseases',
      category: 'Health Areas',
      targetHealthArea: 'Immune Function',
      commonConcerns: 'Frequent infections, autoimmune issues, allergies',
      supportingSupplements: 'Vitamin C, Vitamin D, Zinc, Probiotics'
    }
  },
  {
    id: 'heart-health',
    labels: ['Health Area', 'Cardiovascular'],
    properties: {
      name: 'Heart Health',
      description: 'Cardiovascular system health and function',
      category: 'Health Areas',
      targetHealthArea: 'Cardiovascular System',
      commonConcerns: 'High cholesterol, blood pressure, inflammation',
      supportingSupplements: 'Omega-3, Magnesium, CoQ10'
    }
  },

  // INTERACTIONS & EFFECTS
  {
    id: 'antioxidant-effect',
    labels: ['Effect', 'Mechanism'],
    properties: {
      name: 'Antioxidant Effect',
      description: 'Protection against oxidative stress and free radical damage',
      category: 'Effects',
      mechanism: 'Free radical neutralization, cellular protection',
      benefits: 'Reduced inflammation, cellular protection, aging support'
    }
  },
  {
    id: 'anti-inflammatory-effect',
    labels: ['Effect', 'Mechanism'],
    properties: {
      name: 'Anti-inflammatory Effect',
      description: 'Reduction of inflammation throughout the body',
      category: 'Effects',
      mechanism: 'Inflammatory pathway inhibition, cytokine modulation',
      benefits: 'Reduced pain, improved joint health, disease prevention'
    }
  }
];

export const supplementRelationships: GraphRelationship[] = [
  // Vitamin C relationships
  {
    id: 'rel-1',
    type: 'SUPPORTS',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'vitamin-c',
    target: 'immune-system'
  },
  {
    id: 'rel-2',
    type: 'PRODUCES',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'vitamin-c',
    target: 'antioxidant-effect'
  },
  {
    id: 'rel-3',
    type: 'ENHANCES_ABSORPTION',
    properties: { strength: 'medium', evidenceLevel: 'moderate' },
    source: 'vitamin-c',
    target: 'iron'
  },

  // Vitamin D3 relationships
  {
    id: 'rel-4',
    type: 'SUPPORTS',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'vitamin-d3',
    target: 'immune-system'
  },
  {
    id: 'rel-5',
    type: 'REQUIRES',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'vitamin-d3',
    target: 'magnesium'
  },

  // Omega-3 relationships
  {
    id: 'rel-6',
    type: 'SUPPORTS',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'omega-3-epa-dha',
    target: 'heart-health'
  },
  {
    id: 'rel-7',
    type: 'PRODUCES',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'omega-3-epa-dha',
    target: 'anti-inflammatory-effect'
  },

  // Turmeric relationships
  {
    id: 'rel-8',
    type: 'PRODUCES',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'turmeric-curcumin',
    target: 'anti-inflammatory-effect'
  },
  {
    id: 'rel-9',
    type: 'SYNERGISTIC_WITH',
    properties: { strength: 'medium', evidenceLevel: 'moderate' },
    source: 'turmeric-curcumin',
    target: 'omega-3-epa-dha'
  },

  // Probiotic relationships
  {
    id: 'rel-10',
    type: 'SUPPORTS',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'lactobacillus',
    target: 'immune-system'
  },

  // Zinc relationships
  {
    id: 'rel-11',
    type: 'SUPPORTS',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'zinc',
    target: 'immune-system'
  },
  {
    id: 'rel-12',
    type: 'COMPETES_WITH',
    properties: { strength: 'medium', evidenceLevel: 'moderate' },
    source: 'zinc',
    target: 'iron'
  },

  // Magnesium relationships
  {
    id: 'rel-13',
    type: 'SYNERGISTIC_WITH',
    properties: { strength: 'medium', evidenceLevel: 'moderate' },
    source: 'magnesium',
    target: 'vitamin-b12'
  },

  // Ashwagandha relationships
  {
    id: 'rel-14',
    type: 'SUPPORTS',
    properties: { strength: 'medium', evidenceLevel: 'moderate' },
    source: 'ashwagandha',
    target: 'immune-system'
  },

  // Cross-category synergies
  {
    id: 'rel-15',
    type: 'SYNERGISTIC_WITH',
    properties: { strength: 'high', evidenceLevel: 'strong' },
    source: 'vitamin-c',
    target: 'zinc'
  }
];

export const supplementKnowledgeGraph: GraphData = {
  nodes: supplementNodes,
  relationships: supplementRelationships
};

// Export individual categories for filtering
export const supplementCategories = [
  'Vitamins',
  'Minerals', 
  'Omega Fatty Acids',
  'Probiotics',
  'Amino Acids',
  'Herbal Supplements',
  'Adaptogens',
  'Health Areas',
  'Effects'
];

export const relationshipTypes = [
  'SUPPORTS',
  'PRODUCES', 
  'ENHANCES_ABSORPTION',
  'REQUIRES',
  'SYNERGISTIC_WITH',
  'COMPETES_WITH',
  'CONTRAINDICATED_WITH'
];

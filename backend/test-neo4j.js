const neo4j = require('neo4j-driver');

// Neo4j connection configuration
const NEO4J_URI = 'bolt://localhost:7687';
const NEO4J_USERNAME = 'neo4j';
const NEO4J_PASSWORD = 'password';

async function testNeo4jConnection() {
  const driver = neo4j.driver(
    NEO4J_URI,
    neo4j.auth.basic(NEO4J_USERNAME, NEO4J_PASSWORD)
  );

  const session = driver.session();

  try {
    console.log('🔍 Testing Neo4j connection...');
    
    // Test connection
    const result = await session.run('RETURN 1 as test');
    console.log('✅ Neo4j connection successful!');

    // Check if database has any nodes
    const countResult = await session.run('MATCH (n) RETURN count(n) as nodeCount');
    const nodeCount = countResult.records[0].get('nodeCount').toNumber();
    console.log(`📊 Current node count: ${nodeCount}`);

    if (nodeCount === 0) {
      console.log('📝 Database is empty. Creating sample supplement data...');
      await createSampleData(session);
    } else {
      console.log('📋 Listing existing nodes...');
      await listExistingNodes(session);
    }

  } catch (error) {
    console.error('❌ Neo4j connection failed:', error);
  } finally {
    await session.close();
    await driver.close();
  }
}

async function createSampleData(session) {
  try {
    // Create sample supplements
    const supplements = [
      {
        name: 'Vitamin D3',
        description: 'Essential vitamin for bone health and immune function',
        category: 'Vitamin',
        dosage: '1000-4000 IU daily'
      },
      {
        name: 'Omega-3 Fish Oil',
        description: 'Essential fatty acids for heart and brain health',
        category: 'Fatty Acid',
        dosage: '1-2g daily'
      },
      {
        name: 'Magnesium Glycinate',
        description: 'Highly bioavailable form of magnesium for muscle and nerve function',
        category: 'Mineral',
        dosage: '200-400mg daily'
      },
      {
        name: 'Curcumin',
        description: 'Anti-inflammatory compound from turmeric',
        category: 'Herbal',
        dosage: '500-1000mg daily'
      },
      {
        name: 'Probiotics',
        description: 'Beneficial bacteria for digestive health',
        category: 'Probiotic',
        dosage: '10-50 billion CFU daily'
      }
    ];

    // Create supplement nodes
    for (const supplement of supplements) {
      await session.run(`
        CREATE (s:Supplement {
          id: randomUUID(),
          name: $name,
          description: $description,
          category: $category,
          dosage: $dosage,
          createdAt: datetime(),
          updatedAt: datetime()
        })
      `, supplement);
      console.log(`✅ Created supplement: ${supplement.name}`);
    }

    // Create sample effects
    const effects = [
      { name: 'Bone Health', description: 'Supports strong bones and teeth' },
      { name: 'Immune Support', description: 'Enhances immune system function' },
      { name: 'Heart Health', description: 'Supports cardiovascular function' },
      { name: 'Brain Health', description: 'Supports cognitive function and memory' },
      { name: 'Anti-inflammatory', description: 'Reduces inflammation in the body' },
      { name: 'Digestive Health', description: 'Supports healthy digestion' },
      { name: 'Muscle Function', description: 'Supports muscle contraction and relaxation' }
    ];

    for (const effect of effects) {
      await session.run(`
        CREATE (e:Effect {
          id: randomUUID(),
          name: $name,
          description: $description,
          createdAt: datetime(),
          updatedAt: datetime()
        })
      `, effect);
      console.log(`✅ Created effect: ${effect.name}`);
    }

    // Create relationships between supplements and effects
    const relationships = [
      { supplement: 'Vitamin D3', effect: 'Bone Health' },
      { supplement: 'Vitamin D3', effect: 'Immune Support' },
      { supplement: 'Omega-3 Fish Oil', effect: 'Heart Health' },
      { supplement: 'Omega-3 Fish Oil', effect: 'Brain Health' },
      { supplement: 'Magnesium Glycinate', effect: 'Muscle Function' },
      { supplement: 'Magnesium Glycinate', effect: 'Bone Health' },
      { supplement: 'Curcumin', effect: 'Anti-inflammatory' },
      { supplement: 'Probiotics', effect: 'Digestive Health' },
      { supplement: 'Probiotics', effect: 'Immune Support' }
    ];

    for (const rel of relationships) {
      await session.run(`
        MATCH (s:Supplement {name: $supplementName})
        MATCH (e:Effect {name: $effectName})
        CREATE (s)-[:PROVIDES {
          confidence: 0.8,
          evidenceLevel: 'moderate',
          createdAt: datetime()
        }]->(e)
      `, { supplementName: rel.supplement, effectName: rel.effect });
      console.log(`✅ Created relationship: ${rel.supplement} -> ${rel.effect}`);
    }

    // Create some ingredients
    const ingredients = [
      { name: 'Cholecalciferol', description: 'Active form of Vitamin D3' },
      { name: 'EPA', description: 'Eicosapentaenoic acid, an omega-3 fatty acid' },
      { name: 'DHA', description: 'Docosahexaenoic acid, an omega-3 fatty acid' },
      { name: 'Magnesium', description: 'Essential mineral for many bodily functions' },
      { name: 'Curcuminoids', description: 'Active compounds in turmeric' },
      { name: 'Lactobacillus', description: 'Beneficial probiotic bacteria' }
    ];

    for (const ingredient of ingredients) {
      await session.run(`
        CREATE (i:Ingredient {
          id: randomUUID(),
          name: $name,
          description: $description,
          createdAt: datetime(),
          updatedAt: datetime()
        })
      `, ingredient);
      console.log(`✅ Created ingredient: ${ingredient.name}`);
    }

    // Create relationships between supplements and ingredients
    const ingredientRelationships = [
      { supplement: 'Vitamin D3', ingredient: 'Cholecalciferol' },
      { supplement: 'Omega-3 Fish Oil', ingredient: 'EPA' },
      { supplement: 'Omega-3 Fish Oil', ingredient: 'DHA' },
      { supplement: 'Magnesium Glycinate', ingredient: 'Magnesium' },
      { supplement: 'Curcumin', ingredient: 'Curcuminoids' },
      { supplement: 'Probiotics', ingredient: 'Lactobacillus' }
    ];

    for (const rel of ingredientRelationships) {
      await session.run(`
        MATCH (s:Supplement {name: $supplementName})
        MATCH (i:Ingredient {name: $ingredientName})
        CREATE (s)-[:CONTAINS {
          amount: 'varies',
          createdAt: datetime()
        }]->(i)
      `, { supplementName: rel.supplement, ingredientName: rel.ingredient });
      console.log(`✅ Created relationship: ${rel.supplement} CONTAINS ${rel.ingredient}`);
    }

    console.log('🎉 Sample data created successfully!');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  }
}

async function listExistingNodes(session) {
  try {
    // Get node counts by type
    const result = await session.run(`
      MATCH (n)
      RETURN labels(n) as labels, count(n) as count
      ORDER BY count DESC
    `);

    console.log('📊 Node counts by type:');
    result.records.forEach(record => {
      const labels = record.get('labels');
      const count = record.get('count').toNumber();
      console.log(`  ${labels.join(', ')}: ${count}`);
    });

    // Get relationship counts
    const relResult = await session.run(`
      MATCH ()-[r]->()
      RETURN type(r) as type, count(r) as count
      ORDER BY count DESC
    `);

    console.log('🔗 Relationship counts by type:');
    relResult.records.forEach(record => {
      const type = record.get('type');
      const count = record.get('count').toNumber();
      console.log(`  ${type}: ${count}`);
    });

  } catch (error) {
    console.error('❌ Error listing nodes:', error);
  }
}

// Run the test
testNeo4jConnection();

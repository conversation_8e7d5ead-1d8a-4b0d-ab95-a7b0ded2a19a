import neo4j, { Driver, Session, auth, int } from 'neo4j-driver';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';

class Neo4jConnection {
  private driver: Driver | null = null;
  private isConnected = false;

  async connect(): Promise<void> {
    try {
      logger.info('Connecting to Neo4j database...');
      
      this.driver = neo4j.driver(
        config.neo4j.uri,
        auth.basic(config.neo4j.username, config.neo4j.password),
        {
          maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
          maxConnectionPoolSize: 50,
          connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
          disableLosslessIntegers: true,
        }
      );

      // Verify connectivity
      await this.driver.verifyConnectivity();
      this.isConnected = true;
      
      logger.info('✅ Neo4j connection established successfully');
      
      // Initialize database schema
      await this.initializeSchema();
      
    } catch (error) {
      logError('❌ Failed to connect to Neo4j', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.driver) {
      await this.driver.close();
      this.isConnected = false;
      logger.info('🔌 Neo4j connection closed');
    }
  }

  getDriver(): Driver {
    if (!this.driver || !this.isConnected) {
      throw new Error('Neo4j driver not initialized. Call connect() first.');
    }
    return this.driver;
  }

  async getSession(): Promise<Session> {
    const driver = this.getDriver();
    return driver.session();
  }

  private processParameters(parameters: any): any {
    if (!parameters || typeof parameters !== 'object') {
      return parameters;
    }

    const processed: any = {};
    for (const [key, value] of Object.entries(parameters)) {
      if (typeof value === 'number') {
        // Ensure integers are passed as Neo4j integers, not floats
        if (key === 'limit' || key === 'skip' || key === 'offset' || Number.isInteger(value)) {
          processed[key] = int(Math.floor(value));
        } else {
          processed[key] = value;
        }
      } else {
        processed[key] = value;
      }
    }
    return processed;
  }

  async executeQuery(query: string, parameters: any = {}): Promise<any> {
    const session = await this.getSession();
    const startTime = Date.now();

    try {
      // Convert numeric parameters to proper types for Neo4j
      const processedParams = this.processParameters(parameters);
      const result = await session.run(query, processedParams);
      const duration = Date.now() - startTime;
      
      logDatabaseOperation('Query executed', 'Neo4j', duration, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        recordCount: result.records.length,
      });
      
      return result;
    } catch (error) {
      logError('Neo4j query failed', error, { query, parameters });
      throw error;
    } finally {
      await session.close();
    }
  }

  async executeTransaction(queries: Array<{ query: string; parameters?: any }>): Promise<any[]> {
    const session = await this.getSession();
    const startTime = Date.now();
    
    try {
      const results = await session.executeWrite(async (tx) => {
        const txResults = [];
        for (const { query, parameters = {} } of queries) {
          const result = await tx.run(query, parameters);
          txResults.push(result);
        }
        return txResults;
      });
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Transaction executed', 'Neo4j', duration, {
        queryCount: queries.length,
      });
      
      return results;
    } catch (error) {
      logError('Neo4j transaction failed', error, { queryCount: queries.length });
      throw error;
    } finally {
      await session.close();
    }
  }

  private async initializeSchema(): Promise<void> {
    try {
      logger.info('Initializing Neo4j schema...');
      
      const schemaQueries = [
        // Create constraints for unique identifiers
        'CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE',
        'CREATE CONSTRAINT ingredient_id IF NOT EXISTS FOR (i:Ingredient) REQUIRE i.id IS UNIQUE',
        'CREATE CONSTRAINT effect_id IF NOT EXISTS FOR (e:Effect) REQUIRE e.id IS UNIQUE',
        'CREATE CONSTRAINT study_id IF NOT EXISTS FOR (st:Study) REQUIRE st.id IS UNIQUE',
        'CREATE CONSTRAINT condition_id IF NOT EXISTS FOR (c:Condition) REQUIRE c.id IS UNIQUE',
        'CREATE CONSTRAINT interaction_id IF NOT EXISTS FOR (int:Interaction) REQUIRE int.id IS UNIQUE',

        // Enhanced constraints for therapeutic properties
        'CREATE CONSTRAINT mechanism_id IF NOT EXISTS FOR (m:Mechanism) REQUIRE m.id IS UNIQUE',
        'CREATE CONSTRAINT bioavailability_id IF NOT EXISTS FOR (b:Bioavailability) REQUIRE b.id IS UNIQUE',
        'CREATE CONSTRAINT contraindication_id IF NOT EXISTS FOR (ci:Contraindication) REQUIRE ci.id IS UNIQUE',
        'CREATE CONSTRAINT dosage_id IF NOT EXISTS FOR (d:Dosage) REQUIRE d.id IS UNIQUE',
        'CREATE CONSTRAINT safety_profile_id IF NOT EXISTS FOR (sp:SafetyProfile) REQUIRE sp.id IS UNIQUE',
        'CREATE CONSTRAINT therapeutic_class_id IF NOT EXISTS FOR (tc:TherapeuticClass) REQUIRE tc.id IS UNIQUE',

        // Create indexes for better performance
        'CREATE INDEX supplement_name IF NOT EXISTS FOR (s:Supplement) ON (s.name)',
        'CREATE INDEX ingredient_name IF NOT EXISTS FOR (i:Ingredient) ON (i.name)',
        'CREATE INDEX effect_name IF NOT EXISTS FOR (e:Effect) ON (e.name)',
        'CREATE INDEX study_title IF NOT EXISTS FOR (st:Study) ON (st.title)',
        'CREATE INDEX condition_name IF NOT EXISTS FOR (c:Condition) ON (c.name)',

        // Enhanced indexes for therapeutic queries
        'CREATE INDEX mechanism_pathway IF NOT EXISTS FOR (m:Mechanism) ON (m.pathway)',
        'CREATE INDEX safety_risk_level IF NOT EXISTS FOR (sp:SafetyProfile) ON (sp.riskLevel)',
        'CREATE INDEX dosage_range IF NOT EXISTS FOR (d:Dosage) ON (d.minDose, d.maxDose)',
        'CREATE INDEX study_evidence_level IF NOT EXISTS FOR (st:Study) ON (st.evidenceLevel)',

        // Create full-text search indexes
        'CREATE FULLTEXT INDEX supplement_search IF NOT EXISTS FOR (s:Supplement) ON EACH [s.name, s.description, s.brand, s.commonNames]',
        'CREATE FULLTEXT INDEX ingredient_search IF NOT EXISTS FOR (i:Ingredient) ON EACH [i.name, i.description, i.chemicalName, i.alternativeNames]',
        'CREATE FULLTEXT INDEX effect_search IF NOT EXISTS FOR (e:Effect) ON EACH [e.name, e.description, e.category]',
        'CREATE FULLTEXT INDEX study_search IF NOT EXISTS FOR (st:Study) ON EACH [st.title, st.abstract, st.authors, st.journal]',
        'CREATE FULLTEXT INDEX mechanism_search IF NOT EXISTS FOR (m:Mechanism) ON EACH [m.name, m.description, m.pathway]',
        'CREATE FULLTEXT INDEX condition_search IF NOT EXISTS FOR (c:Condition) ON EACH [c.name, c.description, c.symptoms, c.category]',
      ];

      for (const query of schemaQueries) {
        try {
          await this.executeQuery(query);
        } catch (error: any) {
          // Ignore errors for constraints/indexes that already exist
          if (!error.message.includes('already exists') && !error.message.includes('An equivalent')) {
            throw error;
          }
        }
      }
      
      logger.info('✅ Neo4j schema initialized successfully');
    } catch (error) {
      logError('❌ Failed to initialize Neo4j schema', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.executeQuery('RETURN 1 as health');
      return result.records.length > 0;
    } catch (error) {
      logError('Neo4j health check failed', error);
      return false;
    }
  }

  async getStats(): Promise<any> {
    try {
      const queries = [
        'MATCH (n) RETURN labels(n) as label, count(n) as count',
        'MATCH ()-[r]->() RETURN type(r) as relationship, count(r) as count',
        'CALL db.stats.retrieve("GRAPH COUNTS")',
      ];

      const results = await Promise.all(
        queries.map(query => this.executeQuery(query))
      );

      return {
        nodes: results[0].records.map((record: any) => ({
          label: record.get('label'),
          count: record.get('count').toNumber(),
        })),
        relationships: results[1].records.map((record: any) => ({
          type: record.get('relationship'),
          count: record.get('count').toNumber(),
        })),
        graphCounts: results[2].records[0]?.toObject() || {},
      };
    } catch (error) {
      logError('Failed to get Neo4j stats', error);
      throw error;
    }
  }

  // Helper method to clear all data (use with caution!)
  async clearDatabase(): Promise<void> {
    if (config.isProduction) {
      throw new Error('Cannot clear database in production environment');
    }
    
    try {
      await this.executeQuery('MATCH (n) DETACH DELETE n');
      logger.warn('⚠️ Neo4j database cleared');
    } catch (error) {
      logError('Failed to clear Neo4j database', error);
      throw error;
    }
  }
}

// Create singleton instance
const neo4jConnection = new Neo4jConnection();

// Export connection functions
export const connectNeo4j = () => neo4jConnection.connect();
export const disconnectNeo4j = () => neo4jConnection.disconnect();
export const getNeo4jDriver = () => neo4jConnection.getDriver();
export const getNeo4jSession = () => neo4jConnection.getSession();
export const executeNeo4jQuery = (query: string, parameters?: any) => 
  neo4jConnection.executeQuery(query, parameters);
export const executeNeo4jTransaction = (queries: Array<{ query: string; parameters?: any }>) =>
  neo4jConnection.executeTransaction(queries);
export const neo4jHealthCheck = () => neo4jConnection.healthCheck();
export const getNeo4jStats = () => neo4jConnection.getStats();
export const clearNeo4jDatabase = () => neo4jConnection.clearDatabase();

export default neo4jConnection;

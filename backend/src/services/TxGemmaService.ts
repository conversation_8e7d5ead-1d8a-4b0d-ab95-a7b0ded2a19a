import { config } from '@/config/environment';
import { logger, logError } from '@/utils/logger';
import { cacheGet, cacheSet } from '@/config/redis';
import axios, { AxiosInstance } from 'axios';

interface TherapeuticProperty {
  name: string;
  category: string;
  mechanism: string;
  strength: 'low' | 'medium' | 'high';
  evidence: string;
  confidence: number;
}

interface RelationshipPrediction {
  sourceId: string;
  targetId: string;
  relationshipType: string;
  confidence: number;
  mechanism: string;
  therapeuticBasis: string;
  safetyProfile: {
    riskLevel: 'low' | 'medium' | 'high';
    contraindications: string[];
    warnings: string[];
    interactions: string[];
  };
  evidenceLevel: 'limited' | 'moderate' | 'strong';
  clinicalRelevance: number;
}

interface SafetyAnalysis {
  supplementId: string;
  overallRiskScore: number;
  riskFactors: {
    category: string;
    risk: string;
    severity: 'low' | 'medium' | 'high';
    evidence: string;
  }[];
  contraindications: {
    condition: string;
    severity: 'absolute' | 'relative';
    reason: string;
  }[];
  drugInteractions: {
    drug: string;
    interactionType: string;
    severity: 'minor' | 'moderate' | 'major';
    mechanism: string;
  }[];
  dosageRecommendations: {
    population: string;
    minDose: number;
    maxDose: number;
    unit: string;
    frequency: string;
    duration: string;
  }[];
}

export class TxGemmaService {
  private client: AxiosInstance;
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = config.txgemma?.baseUrl || 'https://api.txgemma.com/v1';
    this.apiKey = config.txgemma?.apiKey || '';
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Suplementor-KnowledgeGraph/1.0'
      }
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.info('TxGemma API Request', { 
          url: config.url, 
          method: config.method,
          dataSize: config.data ? JSON.stringify(config.data).length : 0
        });
        return config;
      },
      (error) => {
        logError('TxGemma API Request Error', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.info('TxGemma API Response', { 
          status: response.status,
          dataSize: JSON.stringify(response.data).length
        });
        return response;
      },
      (error) => {
        logError('TxGemma API Response Error', error, {
          status: error.response?.status,
          statusText: error.response?.statusText
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Analyze therapeutic properties of a supplement
   */
  async analyzeTherapeuticProperties(supplementName: string, description?: string): Promise<TherapeuticProperty[]> {
    const cacheKey = `txgemma:therapeutic:${supplementName}`;
    
    try {
      // Check cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const response = await this.client.post('/analyze/therapeutic-properties', {
        supplement: {
          name: supplementName,
          description: description || '',
        },
        analysis_depth: 'comprehensive',
        include_mechanisms: true,
        include_evidence: true
      });

      const properties: TherapeuticProperty[] = response.data.properties.map((prop: any) => ({
        name: prop.name,
        category: prop.category,
        mechanism: prop.mechanism_of_action,
        strength: prop.therapeutic_strength,
        evidence: prop.evidence_summary,
        confidence: prop.confidence_score
      }));

      // Cache for 24 hours
      await cacheSet(cacheKey, properties, 86400);

      return properties;

    } catch (error) {
      logError('Failed to analyze therapeutic properties', error, { supplementName });
      
      // Return fallback analysis if API fails
      return this.getFallbackTherapeuticProperties(supplementName);
    }
  }

  /**
   * Predict relationships between supplements based on therapeutic properties
   */
  async predictRelationships(
    sourceSupplementId: string, 
    targetSupplementIds: string[],
    context?: { indication?: string; population?: string }
  ): Promise<RelationshipPrediction[]> {
    const cacheKey = `txgemma:relationships:${sourceSupplementId}:${targetSupplementIds.join(',')}`;
    
    try {
      // Check cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const response = await this.client.post('/predict/relationships', {
        source_supplement: sourceSupplementId,
        target_supplements: targetSupplementIds,
        context: context || {},
        prediction_types: [
          'synergistic',
          'antagonistic', 
          'additive',
          'competitive',
          'contraindicated'
        ],
        include_safety_analysis: true,
        include_mechanisms: true
      });

      const predictions: RelationshipPrediction[] = response.data.predictions.map((pred: any) => ({
        sourceId: pred.source_id,
        targetId: pred.target_id,
        relationshipType: pred.relationship_type,
        confidence: pred.confidence_score,
        mechanism: pred.mechanism_description,
        therapeuticBasis: pred.therapeutic_rationale,
        safetyProfile: {
          riskLevel: pred.safety_analysis.risk_level,
          contraindications: pred.safety_analysis.contraindications || [],
          warnings: pred.safety_analysis.warnings || [],
          interactions: pred.safety_analysis.interactions || []
        },
        evidenceLevel: pred.evidence_level,
        clinicalRelevance: pred.clinical_relevance_score
      }));

      // Cache for 12 hours
      await cacheSet(cacheKey, predictions, 43200);

      return predictions;

    } catch (error) {
      logError('Failed to predict relationships', error, { sourceSupplementId, targetSupplementIds });
      
      // Return empty array if API fails
      return [];
    }
  }

  /**
   * Perform comprehensive safety analysis
   */
  async analyzeSafety(supplementId: string, supplementData: any): Promise<SafetyAnalysis> {
    const cacheKey = `txgemma:safety:${supplementId}`;
    
    try {
      // Check cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const response = await this.client.post('/analyze/safety', {
        supplement: {
          id: supplementId,
          name: supplementData.name,
          ingredients: supplementData.ingredients || [],
          dosage_range: supplementData.dosageRange,
          description: supplementData.description
        },
        analysis_scope: 'comprehensive',
        include_drug_interactions: true,
        include_contraindications: true,
        include_population_specific: true
      });

      const safetyAnalysis: SafetyAnalysis = {
        supplementId,
        overallRiskScore: response.data.overall_risk_score,
        riskFactors: response.data.risk_factors.map((rf: any) => ({
          category: rf.category,
          risk: rf.description,
          severity: rf.severity_level,
          evidence: rf.evidence_summary
        })),
        contraindications: response.data.contraindications.map((ci: any) => ({
          condition: ci.condition,
          severity: ci.severity,
          reason: ci.rationale
        })),
        drugInteractions: response.data.drug_interactions.map((di: any) => ({
          drug: di.drug_name,
          interactionType: di.interaction_type,
          severity: di.severity,
          mechanism: di.mechanism
        })),
        dosageRecommendations: response.data.dosage_recommendations.map((dr: any) => ({
          population: dr.target_population,
          minDose: dr.minimum_dose,
          maxDose: dr.maximum_dose,
          unit: dr.dose_unit,
          frequency: dr.frequency,
          duration: dr.duration
        }))
      };

      // Cache for 24 hours
      await cacheSet(cacheKey, safetyAnalysis, 86400);

      return safetyAnalysis;

    } catch (error) {
      logError('Failed to analyze safety', error, { supplementId });
      
      // Return fallback safety analysis
      return this.getFallbackSafetyAnalysis(supplementId);
    }
  }

  /**
   * Batch process multiple supplements for relationship prediction
   */
  async batchPredictRelationships(supplementIds: string[]): Promise<RelationshipPrediction[]> {
    const batchSize = 10; // TxGemma API limit
    const allPredictions: RelationshipPrediction[] = [];

    for (let i = 0; i < supplementIds.length; i += batchSize) {
      const batch = supplementIds.slice(i, i + batchSize);
      
      try {
        const batchPromises = batch.map(sourceId => 
          this.predictRelationships(sourceId, supplementIds.filter(id => id !== sourceId))
        );
        
        const batchResults = await Promise.all(batchPromises);
        allPredictions.push(...batchResults.flat());
        
        // Rate limiting - wait 1 second between batches
        if (i + batchSize < supplementIds.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        logError('Batch prediction failed', error, { batchStart: i, batchSize: batch.length });
      }
    }

    return allPredictions;
  }

  /**
   * Validate predicted relationships against existing knowledge
   */
  async validatePredictions(predictions: RelationshipPrediction[]): Promise<any> {
    try {
      const response = await this.client.post('/validate/predictions', {
        predictions: predictions.map(pred => ({
          source_id: pred.sourceId,
          target_id: pred.targetId,
          relationship_type: pred.relationshipType,
          confidence: pred.confidence
        })),
        validation_criteria: {
          minimum_confidence: 0.7,
          require_evidence: true,
          check_contradictions: true
        }
      });

      return {
        validatedPredictions: response.data.validated_predictions,
        rejectedPredictions: response.data.rejected_predictions,
        validationSummary: response.data.validation_summary
      };

    } catch (error) {
      logError('Failed to validate predictions', error);
      return {
        validatedPredictions: predictions.filter(p => p.confidence >= 0.7),
        rejectedPredictions: predictions.filter(p => p.confidence < 0.7),
        validationSummary: { total: predictions.length, validated: 0, rejected: 0 }
      };
    }
  }

  private getFallbackTherapeuticProperties(supplementName: string): TherapeuticProperty[] {
    // Basic fallback properties based on common supplement knowledge
    const commonProperties: { [key: string]: TherapeuticProperty[] } = {
      'vitamin-c': [{
        name: 'Antioxidant Activity',
        category: 'Antioxidant',
        mechanism: 'Free radical scavenging',
        strength: 'high',
        evidence: 'Well-established',
        confidence: 0.9
      }],
      'omega-3': [{
        name: 'Anti-inflammatory',
        category: 'Inflammation',
        mechanism: 'Prostaglandin modulation',
        strength: 'high',
        evidence: 'Strong clinical evidence',
        confidence: 0.85
      }]
    };

    return commonProperties[supplementName.toLowerCase()] || [];
  }

  private getFallbackSafetyAnalysis(supplementId: string): SafetyAnalysis {
    return {
      supplementId,
      overallRiskScore: 0.3, // Default low-medium risk
      riskFactors: [],
      contraindications: [],
      drugInteractions: [],
      dosageRecommendations: []
    };
  }
}

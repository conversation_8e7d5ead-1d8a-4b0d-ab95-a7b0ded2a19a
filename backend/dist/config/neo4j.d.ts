import { Driver, Session } from 'neo4j-driver';
declare class Neo4jConnection {
    private driver;
    private isConnected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getDriver(): Driver;
    getSession(): Promise<Session>;
    private processParameters;
    executeQuery(query: string, parameters?: any): Promise<any>;
    executeTransaction(queries: Array<{
        query: string;
        parameters?: any;
    }>): Promise<any[]>;
    private initializeSchema;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
    clearDatabase(): Promise<void>;
}
declare const neo4jConnection: Neo4jConnection;
export declare const connectNeo4j: () => Promise<void>;
export declare const disconnectNeo4j: () => Promise<void>;
export declare const getNeo4jDriver: () => Driver;
export declare const getNeo4jSession: () => Promise<Session>;
export declare const executeNeo4jQuery: (query: string, parameters?: any) => Promise<any>;
export declare const executeNeo4jTransaction: (queries: Array<{
    query: string;
    parameters?: any;
}>) => Promise<any[]>;
export declare const neo4jHealthCheck: () => Promise<boolean>;
export declare const getNeo4jStats: () => Promise<any>;
export declare const clearNeo4jDatabase: () => Promise<void>;
export default neo4jConnection;
//# sourceMappingURL=neo4j.d.ts.map